import { INDUSTRY_NEW_TYPE } from './constants'

export const flattenAndFind = (
  array: {
    value: string
    label: string
    children?: { value: string; label: string }[]
  }[],
  targetValue: string,
) => {
  const flattened = array.flatMap((item) =>
    item.children ? [...item.children] : [item],
  )

  return flattened.find((item) => item.value === targetValue)
}

export const getApprovalStatusColor = (item: number) => {
  switch (item) {
    case 2:
      return 'default'
    case 3:
      return 'processing'
    case 4:
      return 'success'
    case 5:
      return 'error'
    default:
      return 'default'
  }
}

export const getIndustryNewTypePath = (targetValue: string) => {
  for (const item of INDUSTRY_NEW_TYPE) {
    if (item.value === targetValue) {
      return [item.label]
    }

    if (item.children && item.children.length > 0) {
      for (const child of item.children) {
        if (child.value === targetValue) {
          return [item.label, child.label]
        }
      }
    }
  }
  return []
}

// Helper function to check if an item matches the target value
const isItemMatch = (
  item: { label: string; value: string },
  target: string,
) => {
  return item.label === target || item.value === target
}

// Helper function to find child value
const findChildValue = (
  children: { label: string; value: string }[],
  target: string,
) => {
  const child = children.find((child) => isItemMatch(child, target))
  return child?.value || ''
}

export const getIndustryNewTypeValue = (targetValue: string[]) => {
  if (!targetValue.length || !targetValue) return ''

  const parentItem = INDUSTRY_NEW_TYPE.find((item) =>
    isItemMatch(item, targetValue[0]),
  )
  if (!parentItem) return ''

  // If only one target value, return parent value
  if (targetValue.length === 1) {
    return parentItem.value
  }

  // If has children and second target value, find child value
  if (parentItem.children?.length && targetValue[1]) {
    return findChildValue(parentItem.children, targetValue[1])
  }

  return ''
}
