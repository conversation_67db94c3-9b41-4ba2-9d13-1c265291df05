import { EllipsisOutlined } from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Link } from '@tanstack/react-router'
import {
  Button,
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Popconfirm,
  Popover,
  Row,
  Select,
  Table,
  type TableColumnsType,
  Tag,
  Tooltip,
  Typography,
} from 'antd'
import dayjs from 'dayjs'
import numeral from 'numeral'
import { useState } from 'react'

import { FormItemPrefix } from '@/components/FormItemPrefix'
import { type APIResponse, request } from '@/lib/request.ts'
import { checkUndefinedProperties } from '@/universal'
import {
  getApprovalStatusColor,
  getIndustryNewTypePath,
} from '@/universal/basic-form'
import {
  APPROVAL_STATUS,
  INDUSTRY_TYPE,
  INVESTOR_TYPE_OPTIONS,
  PROJECT_CATEGORY,
  PROJECT_STATUS,
} from '@/universal/basic-form/constants'
import { ReportModal } from '@/universal/basic-form/Report'
import { TotalPlannedInvestment } from '@/universal/basic-form/TotalPlannedInvestment'
import type { EquityProjectDTO } from '@/universal/basic-form/types.ts'
import { RejectModal } from '@/universal/Reject'

export function EquityProjectsIndexPage() {
  const [form] = Form.useForm()

  const [filters, setFilters] = useState({})
  const [pagination, setPagination] = useState({ page_num: 1, page_size: 10 })

  // const [selectedRows, setSelectedRows] = useState<EquityProjectDTO[]>([])

  const [rejectOpen, setRejectOpen] = useState(false)
  const [rejectKey, setRejectKey] = useState('')

  const [actionPopoverOpen, setActionPopoverOpen] = useState<string | null>(
    null,
  )

  const getTableData = useQuery({
    queryKey: [pagination, filters],
    queryFn: async ({ queryKey: [pagination, filters], signal }) => {
      const response = await request<
        APIResponse<{
          Data: EquityProjectDTO[]
          Total: number
        }>
      >('/equity-investment/list', {
        query: { use_total: true, ...pagination, ...filters },
        signal,
      })
      if (response.code !== 200001) {
        message.error(response.message)
        return null
      }
      return response.data
    },
    staleTime: 0,
    retry: false,
    refetchOnWindowFocus: false,
  })

  const handleDelete = useMutation({
    mutationFn: async (ids: string[]) => {
      const res = await request<APIResponse<null>>('/equity-investment', {
        method: 'DELETE',
        body: { project_ids: ids },
      })
      return res
    },
    onSuccess: (res) => {
      if (res.code === 200001) {
        message.success('操作成功')
        getTableData.refetch()
        // setSelectedRows([])
      } else {
        message.error(res?.message)
      }
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  const handleProjectStatusChange = useMutation({
    mutationFn: async (data: { approval_node_id: string; status: number }) => {
      const res = await request<APIResponse<null>>('/fixed-assets/set-status', {
        method: 'POST',
        body: { ...data },
      })
      return res
    },
    onSuccess: (res) => {
      if (res.code === 200001) {
        message.success('操作成功')
        getTableData.refetch()
        // setSelectedRows([])
      } else {
        message.error(res?.message)
      }
      setActionPopoverOpen(null)
    },
  })

  const columns: TableColumnsType<EquityProjectDTO> = [
    {
      title: '序号',
      align: 'center',
      width: 60,
      render: (_, __, index) => {
        return index + 1
      },
    },
    {
      title: '编制单位',
      dataIndex: 'company_name',
      minWidth: 160,
      ellipsis: {
        showTitle: false,
      },
      render: (value) => (
        <Typography.Text ellipsis={{ tooltip: value }}>{value}</Typography.Text>
      ),
    },
    {
      title: '项目名称',
      dataIndex: 'project_name',
      minWidth: 160,
      ellipsis: {
        showTitle: false,
      },
      render: (value) => (
        <Typography.Text ellipsis={{ tooltip: value }}>{value}</Typography.Text>
      ),
    },
    {
      title: '填报年份',
      dataIndex: 'investment_year',
      minWidth: 100,
    },
    {
      title: '投资分类',
      dataIndex: 'investor_type',
      width: 100,
      render: (value) => {
        return INVESTOR_TYPE_OPTIONS.find((item) => item.value === value)?.label
      },
    },
    {
      title: '项目地点',
      dataIndex: 'project_area',
      minWidth: 120,
    },
    {
      title: '项目分类',
      dataIndex: 'project_category',
      render: (value) => {
        return value
          ?.split(',')
          .map(
            (item: string) =>
              PROJECT_CATEGORY.find((i) => i.value === item)?.label + ',',
          )
      },
    },
    {
      title: '所属行业',
      dataIndex: 'industry_type',
      minWidth: 100,
      ellipsis: {
        showTitle: false,
      },
      render: (value) => {
        return INDUSTRY_TYPE.find((item) => item.value === value)?.label + value
      },
    },
    {
      title: '所属战新产业',
      dataIndex: 'industry_new_type',
      minWidth: 120,
      ellipsis: {
        showTitle: false,
      },
      render: (value) => getIndustryNewTypePath(value).join('/'),
    },
    {
      title: '项目计划总投资（万元）',
      dataIndex: 'project_plan_total_investment',
      minWidth: 180,
      align: 'right',
      ellipsis: {
        showTitle: false,
      },
      render: (value) => (
        <Typography.Text ellipsis={{ tooltip: value }}>
          {numeral(value).format('0,0.00')}
        </Typography.Text>
      ),
    },
    {
      title: '本年计划投资（万元）',
      dataIndex: 'plan_investment_cur_year',
      minWidth: 180,
      align: 'right',
      ellipsis: {
        showTitle: false,
      },
      render: (value) => (
        <Typography.Text ellipsis={{ tooltip: value }}>
          {numeral(value).format('0,0.00')}
        </Typography.Text>
      ),
    },
    {
      title: '本年度完成投资（万元）',
      dataIndex: 'investment_completed_cur_year',
      minWidth: 200,
      align: 'right',
      ellipsis: {
        showTitle: false,
      },
      render: (value) => (
        <Typography.Text ellipsis={{ tooltip: value }}>
          {numeral(value).format('0,0.00')}
        </Typography.Text>
      ),
    },
    {
      title: '投资完成后所占股比',
      dataIndex: 'equity_ratio_after_investmen',
      width: 180,
      align: 'right',
      render: (text) => text + '%',
    },

    {
      title: '状态',
      dataIndex: 'approval_node_status',
      width: 120,
      render: (value, record) => {
        return (
          <Tooltip title={record.reject_reason}>
            <Tag color={getApprovalStatusColor(value)}>
              {APPROVAL_STATUS.find((item) => item.value === value)?.label}
            </Tag>
          </Tooltip>
        )
      },
    },
    {
      title: '最新上报月份',
      dataIndex: 'reprot_month',
      width: 120,
      render: (value) =>
        dayjs(value).format('YYYY-MM') === '1901-01'
          ? ''
          : dayjs(value).format('YYYY-MM'),
    },
    {
      title: '项目状态',
      dataIndex: 'project_status',
      width: 120,
      render: (value) => {
        return PROJECT_STATUS.find((item) => item.value === value)?.label
      },
    },
    {
      title: '最后修改人',
      dataIndex: 'modify_name',
      width: 120,
    },
    {
      title: '最后修改时间',
      dataIndex: 'approval_update_at',
      width: 160,
    },
    {
      title: '操作',
      fixed: 'right',
      render: (_, record) => {
        const actionPopoverKey = record.approval_node_id
        return (
          <>
            <Link
              to="/basic-report/equity-projects/$id/update"
              params={{
                id: record.approval_node_id,
              }}
            >
              <Button type="link" size="small">
                编辑
              </Button>
            </Link>

            {/* <Button type="link" size="small">
              修改记录
            </Button>
            <Divider type="vertical" /> */}
            {record.reject_view === 1 && (
              <Button
                type="link"
                onClick={() => {
                  setRejectKey(record.approval_node_id)
                  setRejectOpen(true)
                }}
                size="small"
              >
                <span className="text-[#CC8B07]">驳回</span>
              </Button>
            )}
            {record.delete_view === 1 && (
              <Popconfirm
                title="确认删除？"
                okText="确认"
                cancelText="取消"
                onConfirm={() => handleDelete.mutate([record.project_id])}
              >
                <Button type="link" danger size="small">
                  删除
                </Button>
              </Popconfirm>
            )}

            <Popover
              trigger="click"
              open={actionPopoverOpen === actionPopoverKey}
              onOpenChange={(open) => {
                setActionPopoverOpen(open ? actionPopoverKey : null)
              }}
              placement="bottom"
              content={
                <div className="flex flex-col">
                  {record.project_status === 2 ? (
                    <Button
                      type="link"
                      size="small"
                      onClick={() =>
                        handleProjectStatusChange.mutate({
                          approval_node_id: record.approval_node_id,
                          status: 1,
                        })
                      }
                      disabled={record.project_status_in_progress_view !== 1}
                    >
                      启动项目
                    </Button>
                  ) : (
                    <Button
                      type="link"
                      size="small"
                      onClick={() =>
                        handleProjectStatusChange.mutate({
                          approval_node_id: record.approval_node_id,
                          status: 2,
                        })
                      }
                      danger
                      disabled={record.project_status_aborted_view !== 1}
                    >
                      中止项目
                    </Button>
                  )}
                  <Popconfirm
                    title="项目结束后无法重新恢复，确认结束吗？"
                    okText="是"
                    cancelText="取消"
                    onConfirm={() =>
                      handleProjectStatusChange.mutate({
                        approval_node_id: record.approval_node_id,
                        status: 3,
                      })
                    }
                  >
                    <Button
                      type="link"
                      size="small"
                      danger
                      disabled={record.project_status_completed_view !== 1}
                    >
                      结束项目
                    </Button>
                  </Popconfirm>
                </div>
              }
            >
              <Button type="link" size="small" icon={<EllipsisOutlined />} />
            </Popover>
          </>
        )
      },
    },
  ]

  return (
    <div className="flex h-full flex-col gap-4">
      <TotalPlannedInvestment plannedUrl="equity-investment/current-total" />
      <Card>
        <div className="flex flex-col gap-4">
          <RejectModal
            open={rejectOpen}
            setOpen={setRejectOpen}
            rejectKey={rejectKey}
            rejectUrl="/equity-investment/reject-mul"
          />

          <Form
            form={form}
            onFinish={(values) => {
              if (checkUndefinedProperties(values)) getTableData.refetch()
              const { date, investment_year, ...data } = values
              const [start_time, end_time] = date || []

              setFilters({
                ...data,
                investment_year:
                  investment_year && dayjs(investment_year).format('YYYY'),
                start_time:
                  start_time && dayjs(start_time).format('YYYY-MM-DD HH:mm:ss'),
                end_time:
                  end_time && dayjs(end_time).format('YYYY-MM-DD HH:mm:ss'),
              })
            }}
            onReset={() => {
              setFilters({})
              setPagination({ page_num: 1, page_size: 10 })
            }}
          >
            <Row gutter={16} className="mb-4">
              <Col span={6}>
                <Form.Item className="!mb-0" name="investment_year">
                  <DatePicker
                    picker="year"
                    format="YYYY"
                    prefix={<FormItemPrefix title="填报年份" />}
                    className="w-full"
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item className="!mb-0" name="project_name">
                  <Input
                    className="w-full"
                    placeholder="请输入"
                    prefix={<FormItemPrefix title="项目名称" />}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item className="!mb-0" name="investor_type">
                  <Select
                    className="w-full"
                    placeholder="请选择"
                    prefix={<FormItemPrefix title="投资分类" />}
                    options={INVESTOR_TYPE_OPTIONS}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item className="!mb-0" name="project_category">
                  <Select
                    className="w-full"
                    placeholder="请选择"
                    prefix={<FormItemPrefix title="项目分类" />}
                    options={PROJECT_CATEGORY}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={6}>
                <Form.Item className="!mb-0" name="industry_type">
                  <Select
                    className="w-full"
                    placeholder="请选择"
                    prefix={<FormItemPrefix title="所属行业" />}
                    options={INDUSTRY_TYPE}
                    showSearch
                    optionFilterProp="label"
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item className="!mb-0" name="approval_status">
                  <Select
                    className="w-full"
                    placeholder="请选择"
                    prefix={<FormItemPrefix title="状态" />}
                    options={APPROVAL_STATUS}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item className="!mb-0" name="date">
                  <DatePicker.RangePicker
                    className="w-full"
                    prefix={<FormItemPrefix title="创建时间" />}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <div className="flex shrink-0 grow-0 items-center justify-end gap-2">
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={getTableData.isLoading}
                  >
                    搜索
                  </Button>
                  <Button
                    type="text"
                    htmlType="reset"
                    loading={getTableData.isLoading}
                  >
                    清空
                  </Button>
                </div>
              </Col>
            </Row>
          </Form>
          <div className="flex items-center justify-end gap-2">
            <Link to="/basic-report/equity-projects/create">
              <Button type="primary">新建数据</Button>
            </Link>
            <ReportModal
              reportUrl="/equity-investment/pending-mul-v2"
              disabled={(getTableData.data?.Data?.length ?? 0) < 1}
            />
            {/* <Button>导入数据</Button>
            <Button>导出数据</Button> */}
            {/* <Popconfirm
              title="确认删除所选项？"
              okText="确认"
              cancelText="取消"
              onConfirm={() =>
                handleDelete.mutate(selectedRows.map((row) => row.project_id))
              }
            >
              <Button
                danger
                disabled={
                  selectedRows.length < 1 ||
                  selectedRows.some((row) => row.delete_view !== 1)
                }
              >
                批量删除
              </Button>
            </Popconfirm> */}
          </div>
          <Table
            loading={getTableData.isFetching}
            size="small"
            rowSelection={{
              type: 'checkbox',
              columnWidth: 40,
              align: 'center',
              // onChange(_, selectedRows) {
              //   setSelectedRows(selectedRows)
              // },
            }}
            dataSource={getTableData.data?.Data}
            columns={columns}
            scroll={{ x: 'max-content' }}
            sticky={{ offsetHeader: 48 }}
            pagination={{
              showQuickJumper: true,
              showSizeChanger: true,
              total: getTableData.data?.Total,
              onChange: (page, pageSize) => {
                setPagination({ page_num: page, page_size: pageSize })
              },
            }}
            rowKey="approval_node_id"
          />
        </div>
      </Card>
    </div>
  )
}
