import { useQuery, useMutation } from '@tanstack/react-query'
import { Link } from '@tanstack/react-router'
import {
  Button,
  Card,
  Form,
  Tag,
  Table,
  Tabs,
  TreeSelect,
  DatePicker,
  Divider,
  Popconfirm,
  Radio,
  Tooltip,
  type TableColumnsType,
  type TableProps,
  type TabsProps,
  Typography,
  message,
} from 'antd'
import { createStyles } from 'antd-style'
import dayjs from 'dayjs'
import { CalendarClockIcon, BadgeJapaneseYenIcon } from 'lucide-react'
import numeral from 'numeral'
import { useMemo, useCallback, useEffect, useState } from 'react'

import { FormItemPrefix } from '@/components/FormItemPrefix'
import SkeletonTable, {
  type SkeletonTableColumnsType,
} from '@/components/SkeletonTable'
import { useApp } from '@/contexts/app'
import { useAuth } from '@/contexts/auth.tsx'
import { useCompanyOptions } from '@/hook/useCompanies'
import { convertToQuarter } from '@/lib/dateUtils'
import { exportExcel } from '@/lib/file'
import { type APIResponse, request } from '@/lib/request'
import { convertSortOrder } from '@/lib/sortUtils'
import { COMPANY_LEVEL } from '@/universal/data-summary/constants.ts'
import {
  INVESTMENT_STATUS_MAP as DATA_FILLING_STATUS_MAP,
  INVESTMENT_STATUS as DATA_FILLING_STATUS,
} from '@/universal/data-summary/constants.ts'
import { RejectModal } from '@/universal/Reject'
import { ReportModal } from '@/universal/Report'

import type { DevelopmentIndexPageDTO } from './type'

interface SearchFormValues {
  company_id?: string // 填报单位
  year?: string // 年份
}

// 修改tabs样式
const useStyle = createStyles(({ css }) => {
  const antCls = '.ant'
  return {
    customCard: css`
      ${antCls}-card-body {
        padding-top: 0;
      }
    `,
  }
})

export function DevelopmentIndexPage() {
  const { styles } = useStyle()
  const { company } = useAuth()
  const { currentDate } = useApp()
  const [form] = Form.useForm()
  const [rejectOpen, setRejectOpen] = useState(false)
  const [selectedRows, setSelectedRows] = useState<DevelopmentIndexPageDTO[]>(
    [],
  )
  const [selectedKeys, setSelectedKeys] = useState<string[]>([])
  const [isReportModalOpen, setIsReportModalOpen] = useState(false)

  const [filters, setFilters] = useState({
    page_num: 1,
    page_size: 10,
    use_total: 1,
    company_id: '',
    consolidation: 0,
    year: '',
    sort_field: '',
    sort_order: '',
    list_style: '',
  })

  // 企业选项
  const { options: companyOptions, isLoading: isLoadingCompanies } =
    useCompanyOptions()

  useEffect(() => {
    if (company?.level && currentDate?.year) {
      const initialConsolidation =
        company?.level === COMPANY_LEVEL.THIRD ? 2 : 1
      setFilters((prev) => ({
        ...prev,
        year: currentDate.year,
        list_style: company?.level === COMPANY_LEVEL.THIRD ? '' : '1',
        consolidation: initialConsolidation,
      }))
      form.setFieldsValue({
        year: dayjs(currentDate.year.toString(), 'YYYY'),
      })
    }
  }, [company?.level, setFilters, currentDate?.year, form])

  // 是否是汇总数据
  const isSummary = useMemo(
    () => filters.consolidation === 1,
    [filters.consolidation],
  )

  // 是否是所有数据
  const isAllData = useMemo(
    () => filters.consolidation === 2,
    [filters.consolidation],
  )

  const isStatisticalQueryEnabled = useMemo(
    () => !!company?.id && !!filters.consolidation && !!currentDate?.year,
    [company?.id, filters.consolidation, currentDate?.year],
  )
  const { data: statisticalData } = useQuery({
    queryKey: [
      '/industry-new/development-index/summary',
      company?.id,
      filters.consolidation,
      currentDate?.year,
    ] as const,
    queryFn: async ({ queryKey: [url, company_id, consolidation, year] }) => {
      const response = await request<APIResponse<DevelopmentIndexPageDTO>>(
        url as string,
        {
          query: {
            company_id,
            consolidation,
            year,
          },
        },
      )
      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
    enabled: isStatisticalQueryEnabled,
  })

  const { data, isLoading, isFetching, refetch } = useQuery({
    queryKey: ['/industry-new/development-index/list', filters] as const,
    queryFn: async ({ queryKey: [url, filters] }) => {
      const response = await request<
        APIResponse<{
          Data: DevelopmentIndexPageDTO[]
          Total: number
        }>
      >(url as string, {
        query: {
          ...filters,
          sort_order: convertSortOrder(filters.sort_order),
        },
      })

      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
    enabled: isAllData || isSummary,
  })

  const handleDelete = useMutation({
    mutationFn: async (ids: string[]) => {
      const res = await request<APIResponse<null>>(
        '/industry-new/development-index',
        {
          method: 'DELETE',
          body: { ids },
        },
      )
      return res
    },
    onSuccess: (res) => {
      if (res.code === 200001) {
        message.success('操作成功')
        refetch()
      } else {
        message.error(res?.message)
      }
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  /** 导出数据 */
  const handleExportData = useCallback(async (id: string) => {
    await exportExcel('/plan-summary/investment-export-excel-data', { id })
  }, [])

  const columns: TableColumnsType<DevelopmentIndexPageDTO> = useMemo(() => {
    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '填报周期',
        dataIndex: 'period',
        width: 120,
        render: (value) => {
          const { year, quarter } = convertToQuarter(value)
          return filters.list_style === '2' ? year : `${year}-Q${quarter}`
        },
      },
      {
        title: '企业名称',
        dataIndex: 'company_name',
        width: 160,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {value}
          </Typography.Text>
        ),
      },
      {
        title: '固定资产投资金额（万元）',
        dataIndex: 'fixed_asset_investment_amount',
        width: 200,
        align: 'right',
        sorter: true,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {numeral(value).format('0,0.00')}
          </Typography.Text>
        ),
      },
      {
        title: '股权投资金额（万元）',
        dataIndex: 'equity_investment_amount',
        width: 180,
        align: 'right',
        sorter: true,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {numeral(value).format('0,0.00')}
          </Typography.Text>
        ),
      },
      {
        title: '上市企业数量（个）',
        dataIndex: 'listed_company_count',
        width: 180,
        align: 'right',
        sorter: true,
        render: (value) => numeral(value).format('0,0'),
      },
      {
        title: '行业领先企业数',
        dataIndex: 'industry_leading_count',
        width: 180,
        align: 'right',
        sorter: true,
        render: (value) => numeral(value).format('0,0'),
      },
      {
        title: '状态',
        dataIndex: 'approval_node_status',
        width: 180,
        render: (value: keyof typeof DATA_FILLING_STATUS_MAP, record) => {
          return !value ? (
            '-'
          ) : value === DATA_FILLING_STATUS.REJECTED ? (
            <Tooltip
              styles={{
                body: {
                  color: '#333',
                },
              }}
              color={'#fff'}
              title={
                <ul>
                  <li>
                    <span className="text-[#FF4C4C]">驳回人:</span>{' '}
                    {record.op_name || '-'}
                  </li>
                  <li>
                    <span className="text-[#FF4C4C]">驳回原因:</span>{' '}
                    {record.reject_reason || '-'}
                  </li>
                  <li>
                    <span className="text-[#FF4C4C]">驳回时间:</span>{' '}
                    {record.approval_update_at}
                  </li>
                </ul>
              }
            >
              <Tag color="error" className="! text-[12px]">
                已驳回
              </Tag>
            </Tooltip>
          ) : (
            <Tag
              color={DATA_FILLING_STATUS_MAP[value]?.type}
              className="! text-[12px]"
            >
              {DATA_FILLING_STATUS_MAP[value]?.label}
            </Tag>
          )
        },
      },
      {
        title: '操作',
        width: 200,
        fixed: 'right',
        render: (_, record) => {
          // 编辑
          const Update = () => {
            const isView =
              record.approval_node_status === DATA_FILLING_STATUS.REJECTED ||
              record.approval_node_status === DATA_FILLING_STATUS.REPORTED
            return (
              <Link
                to={`/strategic-report/development-index/$id/${isView ? 'view' : 'update'}`}
                search={{
                  consolidation: filters.consolidation,
                }}
                params={{ id: record.approval_node_id }}
              >
                <Button
                  type="link"
                  size="small"
                  disabled={!record.approval_node_id}
                >
                  {isView ? '查看' : '编辑'}
                </Button>
              </Link>
            )
          }

          // 上报
          const Report = () => {
            return (
              <Popconfirm
                title="确认上报所选项？"
                okText="确认"
                cancelText="取消"
                onConfirm={() => {
                  setIsReportModalOpen(true)
                  setSelectedKeys([record.approval_node_id])
                }}
              >
                <Button
                  type="link"
                  size="small"
                  disabled={
                    record.approval_node_status === DATA_FILLING_STATUS.REPORTED
                  }
                >
                  上报
                </Button>
              </Popconfirm>
            )
          }

          // 导出
          const Export = () => {
            return (
              <Button
                type="link"
                size="small"
                onClick={() => handleExportData(record.id)}
              >
                导出
              </Button>
            )
          }

          // 驳回
          const Reject = () => {
            return (
              <Button
                type="link"
                size="small"
                danger
                disabled={!record?.reject_view}
                onClick={() => {
                  setSelectedRows([record])
                  setRejectOpen(true)
                }}
              >
                驳回
              </Button>
            )
          }

          // 删除
          const Delete = () => {
            return (
              <Popconfirm
                title="确认删除？"
                okText="确认"
                cancelText="取消"
                onConfirm={() => handleDelete.mutate([record.id])}
              >
                <Button
                  type="link"
                  danger
                  size="small"
                  disabled={!record?.delete_view}
                >
                  删除
                </Button>
              </Popconfirm>
            )
          }

          if (isSummary) {
            return (
              <>
                <Update />
                <Divider className="!m-0" type="vertical" />
                <Report />
                <Divider className="!m-0" type="vertical" />
                <Export />
              </>
            )
          } else {
            return (
              <>
                <Update />
                <Divider className="!m-0" type="vertical" />
                <Export />
                <Divider className="!m-0" type="vertical" />
                {company?.level === COMPANY_LEVEL.THIRD ? (
                  <Report />
                ) : (
                  <Reject />
                )}
                <Divider className="!m-0" type="vertical" />
                <Delete />
              </>
            )
          }
        },
      },
    ]
  }, [
    handleExportData,
    isSummary,
    filters.list_style,
    filters.consolidation,
    handleDelete,
    company?.level,
  ])

  const handleTableChange = useCallback<
    NonNullable<TableProps<DevelopmentIndexPageDTO>['onChange']>
  >(
    (pagination, _filters, sorter) => {
      let sort_field = ''
      let sort_order = ''
      if (Array.isArray(sorter)) {
        if (sorter.length > 0) {
          sort_field = (sorter[0]?.field as string) || ''
          sort_order = sorter[0]?.order || ''
        }
      } else if (sorter) {
        sort_field = (sorter.field as string) || ''
        sort_order = sorter.order || ''
      }
      setFilters((prev) => ({
        ...prev,
        sort_field,
        sort_order,
        page_num: pagination.current || 1,
        page_size: pagination.pageSize || prev.page_size,
      }))
    },
    [setFilters],
  )

  const onSearch = useCallback(
    (values: SearchFormValues) => {
      const next = {
        ...filters,
        page_num: 1,
        company_id: values.company_id || '',
        year: values.year ? dayjs(values.year).format('YYYY') : '',
      }

      if (JSON.stringify(filters) === JSON.stringify(next)) {
        refetch()
      } else {
        setFilters(next)
      }
    },
    [setFilters, refetch, filters],
  )

  const onReset = useCallback(() => {
    form.resetFields()
    setFilters((prev) => ({
      ...prev,
      page_num: 1,
      company_id: '',
      sort_field: '',
      sort_order: '',
      year: '',
    }))
  }, [form, setFilters])

  const tabs: TabsProps['items'] = [
    {
      key: '1',
      label: '汇总数据',
    },
    {
      key: '2',
      label: '所有数据',
    },
  ]

  return (
    <div className="flex h-full flex-col gap-4">
      {/* 驳回组件 */}
      <RejectModal
        open={rejectOpen}
        setOpen={setRejectOpen}
        rejectKey={selectedRows.map((item) => item.approval_node_id)}
        rejectUrl="/industry-new/development-index/reject-mul"
      />
      {/* 上报组件 */}
      <ReportModal
        open={isReportModalOpen}
        onClose={() => setIsReportModalOpen(false)}
        apiUrl="/industry-new/development-index/pending-mul"
        apiParams={{ node_ids: selectedKeys }}
        onSuccess={refetch}
      />

      <Card className={styles.customCard}>
        {company?.level !== COMPANY_LEVEL.THIRD && (
          <Tabs
            activeKey={`${filters.consolidation}`}
            items={tabs}
            onChange={(key) => {
              const { year, company_id } = form.getFieldsValue()
              setFilters((prev) => ({
                ...prev,
                year: year ? dayjs(year).format('YYYY') : '',
                company_id: company_id || '',
                page_num: 1,
                sort_field: '',
                sort_order: '',
                list_style: +key === 1 ? '1' : '',
                consolidation: +key,
              }))
            }}
          />
        )}

        <div className="space-y-6">
          <h2 className="mt-3 text-xl font-semibold">{company?.name}</h2>
          <div className="flex items-center gap-16">
            <div className="flex items-center gap-2">
              <CalendarClockIcon className="size-4" />
              <span className="text-sm text-[#666]">固定资产投资:</span>
              <span className="text-xl font-semibold">
                {numeral(statisticalData?.fixed_asset_investment_amount).format(
                  '0,0.00',
                )}
                万元
              </span>
            </div>
            <div className="flex items-center gap-2">
              <BadgeJapaneseYenIcon className="size-4" />
              <span className="text-sm text-[#666]">股权投资金额:</span>
              <span className="text-xl font-semibold">
                {numeral(statisticalData?.equity_investment_amount).format(
                  '0,0.00',
                )}
                万元
              </span>
            </div>
          </div>
        </div>
      </Card>
      <Card>
        <div className="flex flex-col gap-4">
          <Form form={form} onFinish={onSearch} onReset={onReset}>
            <div className="flex items-end gap-2">
              <div className="grid flex-1 grid-cols-3 gap-4 2xl:grid-cols-4">
                <Form.Item className="!mb-0" name="year">
                  <DatePicker
                    className="w-full"
                    picker="year"
                    format="YYYY"
                    prefix={<FormItemPrefix title="填报年份" />}
                  />
                </Form.Item>
                <Form.Item className="!mb-0" name="company_id">
                  <TreeSelect
                    showSearch
                    placeholder="请选择"
                    allowClear
                    treeDefaultExpandAll
                    loading={isLoadingCompanies}
                    treeData={companyOptions}
                    treeNodeFilterProp="name"
                    fieldNames={{
                      label: 'name',
                      value: 'id',
                    }}
                    prefix={<FormItemPrefix title="企业名称" />}
                  />
                </Form.Item>
              </div>
              <div className="flex shrink-0 grow-0 items-center gap-2">
                <Button type="primary" htmlType="submit">
                  搜索
                </Button>
                <Button type="text" htmlType="reset">
                  清空
                </Button>
              </div>
            </div>
          </Form>
          <div className="flex items-center justify-between gap-2">
            <h3 className="text-[16px] font-[600]">发展指数</h3>
            {isSummary ? (
              <Radio.Group
                value={filters.list_style}
                options={[
                  { value: '1', label: '季度数据' },
                  { value: '2', label: '年度数据' },
                ]}
                onChange={(e) => {
                  const { year, company_id } = form.getFieldsValue()
                  setFilters((prev) => ({
                    ...prev,
                    year: year ? dayjs(year).format('YYYY') : '',
                    company_id: company_id || '',
                    page_num: 1,
                    sort_field: '',
                    sort_order: '',
                    list_style: e.target.value,
                  }))
                }}
              />
            ) : (
              <Link to={`/strategic-report/development-index/create`}>
                <Button type="primary">新建数据</Button>
              </Link>
            )}
          </div>
          <SkeletonTable
            loading={isLoading || isFetching}
            columns={columns as SkeletonTableColumnsType[]}
          >
            <Table
              size="small"
              dataSource={data?.Data ?? []}
              columns={columns}
              scroll={{ x: 'max-content' }}
              sticky={{ offsetHeader: 48 }}
              pagination={{
                showQuickJumper: true,
                showSizeChanger: true,
                total: data?.Total,
              }}
              rowKey="id"
              onChange={handleTableChange}
            />
          </SkeletonTable>
        </div>
      </Card>
    </div>
  )
}
