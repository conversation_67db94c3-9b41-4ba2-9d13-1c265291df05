import { useMutation, useQuery } from '@tanstack/react-query'
import { useNavigate } from '@tanstack/react-router'
import {
  Button,
  Card,
  Col,
  DatePicker,
  Divider,
  Flex,
  Form,
  message,
  Popconfirm,
  Row,
  Select,
  Table,
  Tag,
  Tooltip,
  type TableColumnsType,
} from 'antd'
import dayjs from 'dayjs'
import { useState } from 'react'

import { FormItemPrefix } from '@/components/FormItemPrefix'
import { useAuth } from '@/contexts/auth'
import { useCompany } from '@/contexts/company'
import { exportExcel } from '@/lib/file'
import { request, type APIResponse } from '@/lib/request'
import { checkUndefinedProperties, flattenTreeToArray } from '@/universal'
import { getApprovalStatusColor } from '@/universal/data-summary'
import { APPROVAL_STATUS } from '@/universal/data-summary/constants'
// import { ReportDeadline } from '@/universal/data-summary/ReportDeadline'
import type { MonthlyReportDTO } from '@/universal/data-summary/types'
import { RejectModal } from '@/universal/Reject'

export const ParentCompanyAllDataList = () => {
  const { company } = useAuth()
  const { companyTree } = useCompany()

  const navigate = useNavigate()

  const [pagination, setPagination] = useState({ page_num: 1, page_size: 10 })
  const [queryParams, setQueryParams] = useState({})

  const [selectedRows, setSelectedRows] = useState<MonthlyReportDTO[]>([])

  const [rejectOpen, setRejectOpen] = useState(false)

  const getTableData = useQuery({
    queryKey: [queryParams, pagination],
    queryFn: async ({ queryKey: [queryParams, pagination] }) => {
      const res = await request<
        APIResponse<{ Data: MonthlyReportDTO[]; Total: number }>
      >('/aggregate/monthly-report/list', {
        query: {
          consolidation: 2,
          use_total: 1,
          ...queryParams,
          ...pagination,
        },
      })
      if (res.code === 200001) {
        return res.data
      }
      message.error(res?.message)
      return { Data: [], Total: 0 }
    },
  })

  const handleDelete = useMutation({
    mutationFn: async (ids: string[] | undefined) => {
      const res = await request<APIResponse<null>>(
        '/aggregate/monthly-report',
        {
          method: 'DELETE',
          body: {
            monthly_ids: ids,
          },
        },
      )
      if (res.code === 200001) {
        message.success('操作成功')
        getTableData.refetch()
        return
      }
      message.error(res?.message)
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  const columns: TableColumnsType<MonthlyReportDTO> = [
    {
      title: '序号',
      dataIndex: 'index',
      render: (_, __, index) => index + 1,
    },
    {
      title: '填报周期',
      dataIndex: 'period',
      render: (value) => dayjs(value).format('YYYY-MM'),
    },
    {
      title: '编制单位',
      dataIndex: 'company_name',
      render: (value, record) => {
        const companyLevel = flattenTreeToArray(companyTree)?.filter(
          (item) => item.id === record.company_id,
        )[0]?.level

        return companyLevel === company?.level ? (
          <span>{value + '（本级）'}</span>
        ) : (
          <span>{value}</span>
        )
      },
    },
    {
      title: '截止本月投资(万元)',
      dataIndex: 'total_amount',
      minWidth: 120,
      sorter: (a, b) => a.total_amount - b.total_amount,
    },
    {
      title: '状态',
      dataIndex: 'approval_node_status',
      width: 120,
      render: (value, record) => {
        return (
          <Tooltip title={record.reject_reason}>
            <Tag color={getApprovalStatusColor(value)}>
              {APPROVAL_STATUS.find((item) => item.value === value)?.label}
            </Tag>
          </Tooltip>
        )
      },
    },
    // {
    //   title: '上报截止时间',
    //   dataIndex: 'report_mon_limit',
    //   render: (text) => dayjs(text).format('YYYY-MM-DD'),
    // },
    {
      title: '创建人',
      dataIndex: 'creator_name',
      minWidth: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
      minWidth: 100,
    },
    {
      title: '操作',
      fixed: 'right',
      dataIndex: 'action',
      render: (_, record) => (
        <>
          <Button
            size="small"
            type="link"
            onClick={() =>
              navigate({
                to: '/data-summary/monthly-report/$id/update',
                params: { id: record.id },
                search: {
                  reportPeriod: record.period,
                  companyId: record.company_id,
                },
              })
            }
          >
            编辑
          </Button>
          {/* <Button size="small" type="link">
            修改记录
          </Button> */}
          {record.reject_view === 1 && (
            <Button
              type="link"
              size="small"
              onClick={() => {
                setSelectedRows([record])
                setRejectOpen(true)
              }}
            >
              <span className="text-[#CC8B07]">驳回</span>
            </Button>
          )}

          {record.delete_view === 1 && (
            <Popconfirm
              title="确认删除？"
              okText="确认"
              cancelText="取消"
              onConfirm={() => handleDelete.mutate([record.id])}
            >
              <Button type="link" danger size="small">
                删除
              </Button>
            </Popconfirm>
          )}

          <Button
            size="small"
            type="link"
            onClick={() =>
              exportExcel('aggregate/monthly-report/export-excel-data', {
                id: record.id,
              })
            }
          >
            导出数据
          </Button>
        </>
      ),
    },
  ]

  return (
    <div className="mt-4">
      <RejectModal
        open={rejectOpen}
        setOpen={setRejectOpen}
        rejectKey={selectedRows.map((item) => item.approval_node_id)}
        rejectUrl="/aggregate/monthly-report/reject-mul"
      />

      <Card>
        <Form
          onFinish={(value) => {
            if (checkUndefinedProperties(value)) getTableData.refetch()
            const { period, date, company_id } = value
            const [start_time, end_time] = date || []

            setQueryParams({
              period:
                period &&
                dayjs(period).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
              start_time:
                start_time && dayjs(start_time).format('YYYY-MM-DD HH:mm:ss'),
              end_time:
                end_time && dayjs(end_time).format('YYYY-MM-DD HH:mm:ss'),
              company_id,
            })
          }}
          onReset={() => {
            setQueryParams({})
            setPagination({ page_num: 1, page_size: 10 })
          }}
        >
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item name="period">
                <DatePicker
                  picker="month"
                  prefix={<FormItemPrefix title="填报周期" />}
                  className="w-full"
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="company_name">
                <Select
                  className="w-full"
                  placeholder="请选择填报单位"
                  prefix={<FormItemPrefix title="填报单位" />}
                  showSearch
                  optionFilterProp="label"
                  options={flattenTreeToArray(companyTree).map((item) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="date">
                <DatePicker.RangePicker
                  prefix={<FormItemPrefix title="创建时间" />}
                  className="w-full"
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <div className="flex w-full justify-end gap-2">
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={getTableData.isFetching}
                >
                  搜索
                </Button>
                <Button htmlType="reset" loading={getTableData.isFetching}>
                  清空
                </Button>
              </div>
            </Col>
          </Row>
        </Form>
        <Flex justify="space-between" align="center">
          <div className="font-semibold">投资月报</div>
          <Flex gap={12}>
            <Button
              type="primary"
              onClick={() =>
                navigate({ to: '/data-summary/monthly-report/create' })
              }
            >
              新建数据
            </Button>
            {/* <ReportDeadline date={date} /> */}
            {/* <Button>导入数据</Button>
            <Button>导出数据</Button> */}
            {/* <Button
              danger
              onClick={() => setRejectOpen(true)}
              disabled={
                selectedRows.length < 1 ||
                selectedRows.some((row) => row.reject_view !== 1)
              }
            >
              批量驳回
            </Button> */}
            {/* <Popconfirm
              title="确认删除所选项？"
              okText="确认"
              cancelText="取消"
              onConfirm={() =>
                handleDelete.mutate(selectedRows.map((row) => row.id))
              }
            >
              <Button
                danger
                disabled={
                  selectedRows.length < 1 ||
                  selectedRows.some((row) => row.delete_view !== 1)
                }
              >
                批量删除
              </Button>
            </Popconfirm> */}
          </Flex>
        </Flex>
        <Divider />
        <Table
          loading={getTableData.isFetching}
          columns={columns}
          size="small"
          dataSource={getTableData.data?.Data}
          rowSelection={{
            type: 'checkbox',
            columnWidth: 40,
            align: 'center',
            onChange(_, selectedRows) {
              setSelectedRows(selectedRows)
            },
          }}
          pagination={{
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal(total, range) {
              return `共 ${total} 条数据, 显示第 ${range[0]} - ${range[1]} 条`
            },
            onChange: (page, pageSize) => {
              setPagination({ page_num: page, page_size: pageSize })
            },
          }}
          rowKey="id"
          scroll={{ x: 'max-content' }}
        />
      </Card>
    </div>
  )
}
