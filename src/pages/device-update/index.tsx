import { useQuery, useMutation } from '@tanstack/react-query'
import { Link } from '@tanstack/react-router'
import {
  Button,
  Card,
  Form,
  Tag,
  Table,
  TreeSelect,
  Input,
  Divider,
  Tooltip,
  DatePicker,
  Popconfirm,
  message,
  type TableColumnsType,
  type TableProps,
  Typography,
} from 'antd'
import dayjs from 'dayjs'
import { CalendarClockIcon, BadgeJapaneseYenIcon } from 'lucide-react'
import numeral from 'numeral'
import { useMemo, useCallback, useState, useEffect } from 'react'

import { FormItemPrefix } from '@/components/FormItemPrefix'
import SkeletonTable, {
  type SkeletonTableColumnsType,
} from '@/components/SkeletonTable'
import { useApp } from '@/contexts/app'
import { useAuth } from '@/contexts/auth.tsx'
import { useCompanyOptions } from '@/hook/useCompanies'
import { type APIResponse, request } from '@/lib/request'
import { convertSortOrder } from '@/lib/sortUtils'
import { INDUSTRY_TYPE } from '@/universal/basic-form/constants'
import { COMPANY_LEVEL } from '@/universal/data-summary/constants.ts'
import {
  MULTI_VERSION_STATUS_MAP as DATA_FILLING_STATUS_MAP,
  MULTI_VERSION_STATUS as DATA_FILLING_STATUS,
} from '@/universal/data-summary/constants.ts'
import { RejectModal } from '@/universal/Reject'
import { ReportModal } from '@/universal/Report'

import type { DeviceUpdatePageDTO } from './type'

interface SearchFormValues {
  company_id?: string // 填报单位
  project_name?: string // 项目名称
  year?: string // 年份
}

export function DeviceUpdatePage() {
  const { company } = useAuth()
  const { currentDate } = useApp()
  const [form] = Form.useForm()
  const [rejectOpen, setRejectOpen] = useState(false)
  const [selectedRows, setSelectedRows] = useState<DeviceUpdatePageDTO[]>([])
  const [isReportModalOpen, setIsReportModalOpen] = useState(false)
  const [filters, setFilters] = useState({
    page_num: 1,
    page_size: 10,
    use_total: 1,
    company_id: '',
    project_name: '',
    approval_status: '',
    year: '',
    sort_field: '',
    sort_order: '',
  })

  // 企业选项
  const { options: companyOptions, isLoading: isLoadingCompanies } =
    useCompanyOptions()

  const isStatisticalQueryEnabled = useMemo(() => !!company?.id, [company?.id])

  useEffect(() => {
    if (currentDate?.year) {
      setFilters((prev) => ({
        ...prev,
        year: currentDate.year,
      }))
      form.setFieldsValue({
        year: dayjs(currentDate.year.toString(), 'YYYY'),
      })
    }
  }, [setFilters, currentDate?.year, form])

  const { data: statisticalData } = useQuery({
    queryKey: [
      '/industry-new/major_equipment_replacement/summary',
      company?.id,
      currentDate?.year,
    ] as const,
    queryFn: async ({ queryKey: [url, company_id, year] }) => {
      const response = await request<
        APIResponse<{
          plan: number
          completed: number
        }>
      >(url as string, {
        query: {
          company_id,
          year: year,
        },
      })
      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
    enabled: isStatisticalQueryEnabled,
  })

  const { data, isLoading, isFetching, refetch } = useQuery({
    queryKey: [
      '/industry-new/major_equipment_replacement/list',
      filters,
    ] as const,
    queryFn: async ({ queryKey: [url, filters] }) => {
      const response = await request<
        APIResponse<{
          Data: DeviceUpdatePageDTO[]
          Total: number
        }>
      >(url as string, {
        query: {
          ...filters,
          sort_order: convertSortOrder(filters.sort_order),
        },
      })

      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
  })

  const handleDelete = useMutation({
    mutationFn: async (id: string) => {
      const res = await request<APIResponse<null>>(
        '/industry-new/major_equipment_replacement',
        {
          method: 'DELETE',
          body: { project_ids: [id] },
        },
      )
      return res
    },
    onSuccess: (res) => {
      if (res.code === 200001) {
        message.success('操作成功')
        refetch()
      } else {
        message.error(res?.message)
      }
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  const columns: TableColumnsType<DeviceUpdatePageDTO> = useMemo(() => {
    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '填报年份',
        dataIndex: 'investment_year',
        width: 120,
      },
      {
        title: '项目名称',
        dataIndex: 'project_name',
        width: 120,
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {value}
          </Typography.Text>
        ),
      },
      {
        title: '实施企业名称',
        dataIndex: 'company_name',
        width: 160,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {value}
          </Typography.Text>
        ),
      },
      {
        title: '所属行业',
        dataIndex: 'industry_type',
        width: 150,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => {
          const industry = INDUSTRY_TYPE.find((item) => item.value === value)
          return (
            <Typography.Text ellipsis={{ tooltip: industry?.label }}>
              {industry?.label}
            </Typography.Text>
          )
        },
      },
      {
        title: '项目计划投资（万元）',
        dataIndex: 'project_plan_total_investment',
        width: 180,
        align: 'right',
        sorter: true,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {numeral(value).format('0,0.00')}
          </Typography.Text>
        ),
      },
      {
        title: '实际累计完成投资（万元）',
        dataIndex: 'investment_completed_cur_year',
        width: 200,
        align: 'right',
        sorter: true,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {numeral(value).format('0,0.00')}
          </Typography.Text>
        ),
      },
      {
        title: '状态',
        dataIndex: 'approval_node_status',
        width: 180,
        render: (value: keyof typeof DATA_FILLING_STATUS_MAP, record) => {
          return !value ? (
            '-'
          ) : value === DATA_FILLING_STATUS.REJECTED ||
            value === DATA_FILLING_STATUS.UPDATED_REJECTED ? (
            <Tooltip
              styles={{
                body: {
                  color: '#333',
                },
              }}
              color={'#fff'}
              title={
                <ul>
                  <li>
                    <span className="text-[#FF4C4C]">驳回人:</span>{' '}
                    {record.op_name || '-'}
                  </li>
                  <li>
                    <span className="text-[#FF4C4C]">驳回原因:</span>{' '}
                    {record.reject_reason || '-'}
                  </li>
                  <li>
                    <span className="text-[#FF4C4C]">驳回时间:</span>{' '}
                    {record.approval_update_at}
                  </li>
                </ul>
              }
            >
              <Tag color="error" className="! text-[12px]">
                已驳回
              </Tag>
            </Tooltip>
          ) : (
            <Tag
              color={DATA_FILLING_STATUS_MAP[value]?.type}
              className="! text-[12px]"
            >
              {DATA_FILLING_STATUS_MAP[value]?.label}
            </Tag>
          )
        },
      },
      {
        title: '最后修改时间',
        dataIndex: 'ver_update_at',
        width: 160,
      },
      {
        title: '操作',
        width: 150,
        fixed: 'right',
        render: (_, record) => {
          return (
            <>
              <Link
                to={`/strategic-report/device-update/$id/update`}
                params={{ id: record.approval_node_id }}
              >
                <Button type="link" size="small">
                  编辑
                </Button>
              </Link>
              <Divider className="!m-0" type="vertical" />
              {company?.level !== COMPANY_LEVEL.THIRD && (
                <div className="inline-block">
                  <Button
                    type="link"
                    size="small"
                    danger
                    disabled={!record?.reject_view}
                    onClick={() => {
                      setSelectedRows([record])
                      setRejectOpen(true)
                    }}
                  >
                    驳回
                  </Button>
                  <Divider className="!m-0" type="vertical" />
                </div>
              )}

              <Popconfirm
                title="确认删除？"
                okText="确认"
                cancelText="取消"
                onConfirm={() => handleDelete.mutate(record.project_id)}
              >
                <Button
                  type="link"
                  danger
                  size="small"
                  disabled={!record?.delete_view}
                >
                  删除
                </Button>
              </Popconfirm>
            </>
          )
        },
      },
    ]
  }, [handleDelete, company?.level])

  const handleTableChange = useCallback<
    NonNullable<TableProps<DeviceUpdatePageDTO>['onChange']>
  >(
    (pagination, _filters, sorter) => {
      let sort_field = ''
      let sort_order = ''
      if (Array.isArray(sorter)) {
        if (sorter.length > 0) {
          sort_field = (sorter[0]?.field as string) || ''
          sort_order = sorter[0]?.order || ''
        }
      } else if (sorter) {
        sort_field = (sorter.field as string) || ''
        sort_order = sorter.order || ''
      }
      setFilters((prev) => ({
        ...prev,
        sort_field,
        sort_order,
        page_num: pagination.current || 1,
        page_size: pagination.pageSize || prev.page_size,
      }))
    },
    [setFilters],
  )

  const onSearch = useCallback(
    (values: SearchFormValues) => {
      const next = {
        ...filters,
        page_num: 1,
        project_name: values.project_name || '',
        company_id: values.company_id || '',
        year: values.year ? dayjs(values.year).format('YYYY') : '',
      }

      if (JSON.stringify(filters) === JSON.stringify(next)) {
        refetch()
      } else {
        setFilters(next)
      }
    },
    [setFilters, refetch, filters],
  )

  const onReset = useCallback(() => {
    form.resetFields()
    setFilters((prev) => ({
      ...prev,
      page_num: 1,
      company_id: '',
      sort_field: '',
      sort_order: '',
      project_name: '',
      year: '',
    }))
  }, [form, setFilters])

  return (
    <div className="flex h-full flex-col gap-4">
      {/* 驳回组件 */}
      <RejectModal
        open={rejectOpen}
        setOpen={setRejectOpen}
        rejectKey={selectedRows.map((item) => item.approval_node_id)}
        rejectUrl="/industry-new/major_equipment_replacement/reject-mul"
      />
      {/* 上报组件 */}
      <ReportModal
        open={isReportModalOpen}
        onClose={() => setIsReportModalOpen(false)}
        apiUrl="/industry-new/major_equipment_replacement/pending-mul"
        apiParams={{ year: currentDate?.year.toString() }}
        onSuccess={refetch}
      />
      <Card>
        <div className="space-y-6">
          <h2 className="mt-3 text-xl font-semibold">{company?.name}</h2>
          <div className="flex items-center gap-16">
            <div className="flex items-center gap-2">
              <CalendarClockIcon className="size-4" />
              <span className="text-sm text-[#666]">本年度计划投资总额:</span>
              <span className="text-xl font-semibold">
                {numeral(statisticalData?.plan).format('0,0.00')}
                万元
              </span>
            </div>
            <div className="flex items-center gap-2">
              <BadgeJapaneseYenIcon className="size-4" />
              <span className="text-sm text-[#666]">本年度累计已投资总额:</span>
              <span className="text-xl font-semibold">
                {numeral(statisticalData?.completed).format('0,0.00')}
                万元
              </span>
            </div>
          </div>
        </div>
      </Card>
      <Card>
        <div className="flex flex-col gap-4">
          <Form form={form} onFinish={onSearch} onReset={onReset}>
            <div className="flex items-end gap-2">
              <div className="grid flex-1 grid-cols-3 gap-4 2xl:grid-cols-4">
                <Form.Item className="!mb-0" name="year">
                  <DatePicker
                    className="w-full"
                    picker="year"
                    format="YYYY"
                    prefix={<FormItemPrefix title="填报年份" />}
                  />
                </Form.Item>
                <Form.Item className="!mb-0" name="project_name">
                  <Input
                    placeholder="请输入"
                    allowClear
                    prefix={<FormItemPrefix title="项目名称" />}
                  />
                </Form.Item>
                <Form.Item className="!mb-0" name="company_id">
                  <TreeSelect
                    showSearch
                    placeholder="请选择"
                    allowClear
                    treeDefaultExpandAll
                    loading={isLoadingCompanies}
                    treeData={companyOptions}
                    treeNodeFilterProp="name"
                    fieldNames={{
                      label: 'name',
                      value: 'id',
                    }}
                    prefix={<FormItemPrefix title="实施企业名称" />}
                  />
                </Form.Item>
              </div>
              <div className="flex shrink-0 grow-0 items-center gap-2">
                <Button type="primary" htmlType="submit">
                  搜索
                </Button>
                <Button type="text" htmlType="reset">
                  清空
                </Button>
              </div>
            </div>
          </Form>
          <div className="flex items-center justify-between gap-2">
            <h3 className="text-[16px] font-[600]">大规模设备更新</h3>

            <div className="flex items-center gap-2">
              <Link to={`/strategic-report/device-update/create`}>
                <Button type="primary">新建数据</Button>
              </Link>
              {company?.level === COMPANY_LEVEL.GROUP ? (
                <Button>上报国资委</Button>
              ) : (
                <Button onClick={() => setIsReportModalOpen(true)}>
                  一键上报
                </Button>
              )}

              <Button>导出数据</Button>
            </div>
          </div>
          <SkeletonTable
            loading={isLoading || isFetching}
            columns={columns as SkeletonTableColumnsType[]}
          >
            <Table
              size="small"
              dataSource={data?.Data ?? []}
              columns={columns}
              scroll={{ x: 'max-content' }}
              sticky={{ offsetHeader: 48 }}
              pagination={{
                showQuickJumper: true,
                showSizeChanger: true,
                total: data?.Total,
              }}
              rowKey="project_id"
              onChange={handleTableChange}
            />
          </SkeletonTable>
        </div>
      </Card>
    </div>
  )
}
