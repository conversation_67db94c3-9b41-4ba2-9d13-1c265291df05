import { message } from 'antd'
import dayjs from 'dayjs'
import type { FetchOptions } from 'ofetch'

import { request } from './request'

/**
 * 通用文件导出函数
 * @param url 导出接口URL
 * @param params 导出参数
 * @param options 可选配置项
 * @returns Promise<void>
 */
export async function exportFile(
  url: string,
  params?: Record<string, unknown>,
  options?: {
    /** 自定义文件名 */
    fileName?: string
    /** 导出前的加载提示 */
    loadingMessage?: string
    /** 导出成功提示 */
    successMessage?: string
    /** 导出失败提示 */
    errorMessage?: string
    /** ofetch请求选项 */
    requestOptions?: FetchOptions
  },
): Promise<void> {
  let loadingShown = false
  try {
    // 显示加载提示并记录状态
    if (options?.loadingMessage) {
      message.loading(options.loadingMessage, 0)
      loadingShown = true
    }

    // 发送请求获取文件数据
    const { _data, headers } = await request.raw(url, {
      method: 'GET',
      query: params,
      ...options?.requestOptions,
    })

    // 检查响应是否为JSON错误格式
    const contentType = headers?.get('content-type') || ''
    if (
      contentType.includes('application/json') ||
      contentType.includes('text/plain')
    ) {
      try {
        // 尝试将响应解析为JSON
        const jsonData = typeof _data === 'string' ? JSON.parse(_data) : _data

        // 检查是否是错误响应格式
        if (
          jsonData &&
          typeof jsonData === 'object' &&
          jsonData.code &&
          jsonData.code !== 200001
        ) {
          throw new Error(
            typeof jsonData.message === 'string'
              ? jsonData.message
              : JSON.stringify(jsonData.message),
          )
        }
      } catch (jsonError) {
        // 如果不是有效的JSON，继续处理为文件
        if (!(jsonError instanceof SyntaxError)) {
          throw jsonError
        }
      }
    }

    // 获取实际的文件类型
    const fileContentType = contentType || 'application/octet-stream'

    // 创建Blob对象
    const blob = new Blob([_data as BlobPart], {
      type: fileContentType,
    })

    // 处理文件名
    let fileName = options?.fileName
    if (!fileName) {
      const contentDisposition = headers?.get('content-disposition')
      if (contentDisposition) {
        const fileNameMatch = contentDisposition.match(/filename="?([^;"]+)"?/)
        if (fileNameMatch && fileNameMatch[1]) {
          fileName = decodeURIComponent(fileNameMatch[1])
        }
      }
    }

    if (!fileName) {
      fileName = `export_${dayjs(new Date()).format('YYYY-MM-DD_HH:mm:ss')}`
    }

    // 触发下载
    const urlCreator = window.URL || window.webkitURL
    const objectUrl = urlCreator.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = objectUrl
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    setTimeout(() => {
      urlCreator.revokeObjectURL(objectUrl)
    }, 100)

    // 显示成功提示
    if (options?.successMessage) {
      message.success(options.successMessage)
    }
  } catch (error) {
    console.error('文件导出失败:', error)
    // 错误时先销毁加载提示，再显示错误提示
    if (loadingShown) {
      message.destroy()
      loadingShown = false
    }
    // 显示错误提示
    message.error(options?.errorMessage || '文件导出失败，请稍后重试')
  } finally {
    // 只有在成功且仍显示加载提示的情况下才销毁
    if (loadingShown) {
      message.destroy()
    }
  }
}

/**
 * 简化版的Excel导出函数
 * @param url 导出接口URL
 * @param params 导出数据的参数
 * @param fileName 可选的自定义文件名
 */
export async function exportExcel(
  url: string,
  params?: Record<string, unknown>,
  fileName?: string,
): Promise<void> {
  return exportFile(url, params, {
    fileName,
    loadingMessage: '正在导出数据...',
    successMessage: '数据导出成功',
    errorMessage: '数据导出失败',
  })
}
